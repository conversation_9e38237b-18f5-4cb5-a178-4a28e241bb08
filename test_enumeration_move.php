<?php

// Test script to verify that enumeration column has been moved from Excel adapter to ModelTableProvider

require_once '_libs/inc/common/registry.class.php';
require_once '_libs/tests/Nzoom/TestHelpers/GlobalMocks.php';
require_once '_libs/Nzoom/Export/Provider/ExportTableProviderInterface.php';
require_once '_libs/Nzoom/Export/Provider/ModelTableProvider.php';
require_once '_libs/Nzoom/Export/Entity/ExportTable.php';
require_once '_libs/Nzoom/Export/Entity/ExportTableCollection.php';
require_once '_libs/Nzoom/Export/Entity/ExportHeader.php';
require_once '_libs/Nzoom/Export/Entity/ExportColumn.php';
require_once '_libs/Nzoom/Export/Entity/ExportRecord.php';
require_once '_libs/Nzoom/Export/Entity/ExportValue.php';



echo "Testing enumeration column move from Excel adapter to ModelTableProvider...\n\n";

// Create registry
$registry = new Registry();

// Create ModelTableProvider
$provider = new \Nzoom\Export\Provider\ModelTableProvider($registry, 'full_num', 'Full Num');

// Create mock model
$model = new MockModel(1, 'DOC-001');

// Get tables for the record
$tableCollection = $provider->getTablesForRecord($model);

echo "Number of tables found: " . $tableCollection->count() . "\n";

foreach ($tableCollection->getTables() as $table) {
    echo "\nTable: " . $table->getTableName() . "\n";
    echo "Table Type: " . $table->getTableType() . "\n";
    
    $header = $table->getHeader();
    $columns = $header->getColumns();
    
    echo "Columns (" . count($columns) . "):\n";
    foreach ($columns as $index => $column) {
        echo "  " . ($index + 1) . ". " . $column->getVarName() . " (" . $column->getLabel() . ") - " . $column->getType() . "\n";
    }
    
    echo "Records (" . $table->count() . "):\n";
    foreach ($table->getRecords() as $recordIndex => $record) {
        echo "  Record " . ($recordIndex + 1) . ":\n";
        $values = $record->getValues();
        foreach ($values as $valueIndex => $exportValue) {
            $column = $columns[$valueIndex] ?? null;
            $columnName = $column ? $column->getVarName() : "Column $valueIndex";
            echo "    $columnName: " . $exportValue->getValue() . "\n";
        }
    }
}

echo "\nTest completed!\n";
echo "\nExpected results:\n";
echo "- Each table should have reference column as first column (full_num)\n";
echo "- Each table should have enumeration column as second column (#)\n";
echo "- Enumeration values should be 1, 2, 3 for the three records\n";
echo "- Data columns should follow after enumeration column\n";
